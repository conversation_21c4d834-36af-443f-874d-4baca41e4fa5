# Theme Persistence Bug Fix Plan

## Background and Motivation

A critical serialization bug prevents conversational themes from persisting between app sessions. This plan outlines a direct fix to the serialization process to ensure theme data is reliably stored and restored into `AgentCortex` on app launch, which is a primary goal for the MVP.

## Critical Issue: Double JSON Serialization Bug

-   **Problem**: The `metadata` field in the database contains double-escaped JSON (`{"themes":"[\"title\":\"Theme\"]"}`), which fails to parse.
-   **Root Cause**: Theme data is serialized twice—first into a `String` in `ConversationAgent`, and then that string is serialized again as part of a `Map` in `ConversationRepository`.

## The Agreed Solution: Fix Serialization & Verify Restoration

We will fix the bug by correcting the data flow to use `kotlinx.serialization.json.JsonElement` and a Room `TypeConverter`. This prevents double serialization. We will then verify and adapt the existing `SessionContextManager` to correctly restore the properly-formatted themes into `AgentCortex`.

### A Note on Backward Compatibility & Database Migration

-   **Crash Prevention Only**: The app must not crash when encountering old, malformed metadata. It will not attempt to parse it functionally.
-   **No Database Migration Needed**: The `TypeConverter` translates our in-memory `Map<String, JsonElement>` to a `String` for the database. The database schema does not change, so no migration is required.

### Implementation Plan

#### Task 1: Update Data Models and Type Converters (`ConversationLog.kt`, `Converters.kt`, `ManifestationDatabase.kt`)
**Objective**: Modify the data representation to handle structured JSON and provide the necessary database converters.

1.  **Update `ConversationLogEntry` (`ConversationLog.kt`)**: Change the `metadata` property type from `String?` to `Map<String, JsonElement>?`.
2.  **Create `TypeConverter` (`data/Converters.kt`)**: Add a `MetadataTypeConverter` class to `Converters.kt` with functions to convert `Map<String, JsonElement>?` to/from `String?`. The loading function will include a `try-catch` block that returns an `emptyMap()` on failure to prevent crashes.
3.  **Register `TypeConverter` (`ManifestationDatabase.kt`)**: Uncomment the `@TypeConverters` annotation on the `ManifestationDatabase` class and add our new `MetadataTypeConverter` to it.
4.  **Simplify `toConversationEntry()` (`ConversationLog.kt`)**: Remove the complex parsing logic from this function, as it is now handled by the `TypeConverter`.

#### Task 2: Refactor the Repository (`ConversationRepository.kt`)
**Objective**: Remove the flawed manual serialization.

1.  **Update `logConversation` Signature**: Change the `metadata` parameter type to `Map<String, JsonElement>`.
2.  **Remove Manual JSON Building**: Delete the `StringBuilder` logic (lines 84-109) that was causing the double serialization.
3.  **Simplify `ConversationLogEntry` Creation**: Pass the `Map<String, JsonElement>` metadata object directly to the constructor. Room will now use the `TypeConverter` automatically.

#### Task 3: Update the Agent Logic (`ConversationAgent.kt`)
**Objective**: Ensure the agent creates metadata in the new, correct format.

1.  **Update `addToHistory`**: Change the `metadata` parameter to accept `Map<String, JsonElement>`.
2.  **Modify Metadata Creation**: In `handleContinueCheckIn` and `handleStructuredTransition`, change theme serialization from `encodeToString` to `encodeToJsonElement`.
3.  **Construct the Map**: Build the metadata map as a `Map<String, JsonElement>`.

#### Task 4: Verify and Adapt Theme Restoration (`SessionContextManager.kt`)
**Objective**: Ensure the existing `SessionContextManager` can correctly parse the new, properly-formatted theme data.

1.  **Locate `SessionContextManager.kt`**: Confirm the existing implementation.
2.  **Adapt `extractThemesFromHistory()`**: Modify this function's parsing logic. It will now receive a `JsonElement` for the "themes" key instead of a string. It needs to be updated to deserialize directly from this `JsonElement`.
3.  **Verify Integration**: Confirm that `SessionContextManager.loadSessionContext()` is called correctly on app startup and that the restored themes flow into `AgentCortex`.

### Success Criteria
-   **Bug is Fixed**: The double-serialization error is eliminated.
-   **State is Restored**: On launch, `AgentCortex` is populated with themes from the end of the last session.
-   **No App Crashes**: The app is robust against old, malformed data.

## Next Immediate Actions

1.  **Execute Task 1**: Modify `ConversationLog.kt`, `data/Converters.kt`, and `ManifestationDatabase.kt`.
2.  **Execute Task 2**: Refactor `ConversationRepository.kt`.
3.  **Execute Task 3**: Update `ConversationAgent.kt`.
4.  **Execute Task 4**: Verify and adapt `SessionContextManager.kt`.
5.  **Test**: Run all tests outlined below.

## Testing Plan

1.  **New Conversation Test**: Confirm themes are generated and saved as clean JSON.
2.  **Persistence & Restoration Test**: Restart the app and confirm themes from the previous session are correctly displayed.
3.  **Legacy Data Test**: Confirm the app does not crash when encountering old data.